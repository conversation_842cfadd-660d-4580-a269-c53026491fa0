<div class="mb-5 mt-0">
  <app-broker-title *ngIf="user?.role === 'broker'"></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <div class="d-flex my-4">
            <h1 class="text-dark-blue fs-2 fw-bolder me-1 mt-3">Projects</h1>
          </div>
          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <app-keenicon name="magnifier"
                class="fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3"
                type="outline"></app-keenicon>
              <input type="text" name="searchText"
                class="form-control form-control-flush ps-10 bg-light border rounded-pill" [(ngModel)]="searchText"
                (ngModelChange)="onSearchTextChange($event)" placeholder="Search By Project Name.."
                data-kt-search-element="input" />
            </form>
          </div>
          <div class="d-flex my-4">
            <div class="position-relative me-3">
              <a class="btn btn-sm btn-light-dark-blue me-3 cursor-pointer" (click)="toggleFilterDropdown()">
                <i class="fa-solid fa-filter"></i> Filter
              </a>

              <!-- Filter Dropdown -->
              <div *ngIf="isFilterDropdownVisible" class="dropdown-menu show p-4 shadow"
                style="position: absolute; top: 100%; right:0px; z-index: 1000; min-width: 200px; width: auto;">
                <app-project-filter (filtersApplied)="onFiltersApplied($event)"></app-project-filter>
              </div>
            </div>

            <a *ngIf="user?.role === 'developer'" [routerLink]="['/developer/projects/create']"
              class="btn btn-sm btn-dark-blue cursor-pointer">
              <i class="fa-solid fa-plus"></i>
              Create Project
            </a>
          </div>
        </div>
      </div>
    </div>

    <app-empty-properties-card *ngIf="showEmptyCard" [userRole]="user?.role"></app-empty-properties-card>

    <div class="table-responsive mb-5" *ngIf="!showEmptyCard">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px ps-4 rounded-start">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('name')">
              project
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("name")
                }}</span>
            </th>
            <!-- <th class="min-w-100px">
              Type
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th> -->
            <th class="min-w-100px cursor-pointer" (click)="sortData('city_id')">
              Area
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("city_id")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('buildings_count')">
              no of buildings
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("buildings_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('apartments_count')">
              no of apartments
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("apartments_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('villas_count')">
              no of villas
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("villas_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('duplex_count')">
              no of duplex
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("duplex_count")
                }}</span>
            </th>
            <th class="min-w-150px cursor-pointer" (click)="sortData('commercial_units_count')">
              properties
              <span class="ms-1 text-primary fw-bold">{{
                getSortArrow("commercial_units_count")
                }}</span>
            </th>
            <!-- <th class="min-w-100px">
              brokers
              <i class="fa-solid fa-arrow-down text-dark-blue ms-1"></i>
            </th> -->
            <th class="min-w-100px text-end rounded-end pe-4"></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of rows">
            <td class="ps-4">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-45px me-5">
                  <img [src]="row.logoImage" alt="img" class="rounded-circle" />
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <!-- [queryParams]="{ id: row.id }" -->
                  <a [routerLink]="['/developer/projects/models']" [queryParams]="{ projectId: row.id }"
                    class="text-gray-900 fw-bold text-hover-dark-blue fs-6">
                    {{ row.name }}
                  </a>
                  <span class="text-muted fs-7">{{ row.area.name_en }}</span>
                </div>
              </div>
            </td>
            <!-- <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.projectType }}
              </span>
            </td> -->
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.city.name_en }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.buildingsCount }}
              </span>
            </td>
            <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.apartmentsCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.villasCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                {{ row.duplexCount }}
              </span>
            </td>
            <td>
              <span class="fw-bold badge fs-6 fw-semibold">
                Commercial: {{ row.commercialUnitsCount }}<br />
                Administrative: {{ row.administrativeUnitsCount }}
              </span>
            </td>
            <!-- <td>
              <span class="text-gray-800 fw-semibold d-block mb-1 fs-6">
                {{ row.brokers }}
              </span>
            </td> -->
            <td class="text-end pe-4">
              <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
              </button>
              <app-projects-dropdown-action-menu [id]="row.id"></app-projects-dropdown-action-menu>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <!-- Pagination -->
  <div *ngIf="!loading && rows.length > 0" class="d-flex justify-content-center mt-5 mb-5">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>

<router-outlet></router-outlet>
