import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProjectsComponent } from './projects/projects.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';
import { BrokerModule } from '../broker/broker.module';
import { ModelsComponent } from './projects/components/models/models.component';
import { UnitsComponent } from './projects/components/units/units.component';
import { DevelopersComponent } from './developers.component';
import { PaginationComponent } from '../../pagination/pagination.component';
import { DeveloperDashboardComponent } from './developer-dashboard/developer-dashboard.component';
import { DeveloperDashboardModule } from './developer-dashboard/developer-dashboard.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AddPropertyComponent } from './add-property-dev/add-property.component';
import { UpdatePropertyComponent } from './update-property-dev/update-property.component';
import { ProjectsDropdownActionMenuComponent } from './projects/components/projects-dropdown-action-menu/projects-dropdown-action-menu.component';
// import { DropdownActionMenuComponent } from './projects/components/dropdown-action-menu/dropdown-action-menu.component';
import { ContractRequestsComponent } from './projects/components/contract-requests/contract-requests.component';
import { ContractRequestsDropdownActionMenuComponent } from './projects/components/contract-requests/contract-requests-dropdown-action-menu/contract-requests-dropdown-action-menu.component';
import { UnitDetailsComponent } from './projects/components/unit-details/unit-details.component';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { ModelUnitFilterComponent } from './projects/components/model-unit-filter/model-unit-filter.component';
import { ModelFilterComponent } from './projects/components/model-filter/model-filter.component';
import { ProjectFilterComponent } from './projects/components/project-filter/project-filter.component';

@NgModule({
  declarations: [
    ProjectsComponent,
    ProjectsDropdownActionMenuComponent,
    ModelsComponent,
    UnitsComponent,
    UnitDetailsComponent,
    DevelopersComponent,
    ContractRequestsComponent,
    ContractRequestsDropdownActionMenuComponent,
    ModelUnitFilterComponent,
    ModelFilterComponent,
    ProjectFilterComponent,
  ],
  imports: [
    CommonModule,
    BrokerModule,
    DeveloperDashboardModule,
    ReactiveFormsModule,
    FormsModule,
    NgbModalModule,
    RouterModule.forChild([
      { path: '', component: DevelopersComponent },
      { path: 'dashboards', component: DeveloperDashboardComponent },
      { path: 'projects', component: ProjectsComponent },
      // { path: 'projects/:developerId', component: ProjectsComponent },
      { path: 'projects/create', component: AddPropertyComponent },
      { path: 'projects/edit', component: UpdatePropertyComponent },

      { path: 'projects/models', component: ModelsComponent },
      { path: 'projects/models/units', component: UnitsComponent },
      {
        path: 'projects/models/units/details',
        component: UnitDetailsComponent,
      },
      { path: 'projects/requests', component: ContractRequestsComponent },
    ]),
    SharedModule,
    PaginationComponent,
  ],
  exports: [
    //DropdownActionMenuComponent,
    ModelUnitFilterComponent,
    ModelFilterComponent,
    ProjectFilterComponent,
  ],
})
export class DeveloperModule {}
